const cloudinary = require('cloudinary').v2;
const multipart = require('parse-multipart-data');

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET
});

exports.handler = async (event, context) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'POST, OPTIONS'
      }
    };
  }

  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  try {
    // Parse multipart form data
    const boundary = event.headers['content-type'].split('boundary=')[1];
    const parts = multipart.parse(Buffer.from(event.body, 'base64'), boundary);
    
    let projectData = {
      title: '',
      description: '',
      type: '',
      beforeImages: [],
      afterImages: [],
      videos: []
    };

    // Process each part of the form
    for (const part of parts) {
      const { name, data, filename, type } = part;
      
      if (name === 'projectTitle') {
        projectData.title = data.toString();
      } else if (name === 'projectDescription') {
        projectData.description = data.toString();
      } else if (name === 'projectType') {
        projectData.type = data.toString();
      } else if (filename) {
        // This is a file upload
        try {
          const uploadResult = await new Promise((resolve, reject) => {
            const uploadOptions = {
              folder: 'portfolio',
              resource_type: 'auto',
              public_id: `${Date.now()}_${filename.replace(/[^a-zA-Z0-9.]/g, '_')}`,
              transformation: type.startsWith('image/') ? [
                { width: 1200, height: 800, crop: 'limit', quality: 'auto' }
              ] : undefined
            };

            cloudinary.uploader.upload_stream(
              uploadOptions,
              (error, result) => {
                if (error) reject(error);
                else resolve(result);
              }
            ).end(data);
          });

          const fileInfo = {
            url: uploadResult.secure_url,
            publicId: uploadResult.public_id,
            filename: filename,
            size: data.length,
            type: type
          };

          // Categorize the uploaded file
          if (name === 'beforeImages') {
            projectData.beforeImages.push(fileInfo);
          } else if (name === 'afterImages') {
            projectData.afterImages.push(fileInfo);
          } else if (name === 'videos') {
            projectData.videos.push(fileInfo);
          }

        } catch (uploadError) {
          console.error('Cloudinary upload error:', uploadError);
          throw new Error(`Failed to upload ${filename}: ${uploadError.message}`);
        }
      }
    }

    // Validate required fields
    if (!projectData.title || !projectData.description || !projectData.type) {
      return {
        statusCode: 400,
        headers: { 'Access-Control-Allow-Origin': '*' },
        body: JSON.stringify({
          success: false,
          error: 'Missing required fields: title, description, or type'
        })
      };
    }

    if (projectData.beforeImages.length === 0 || projectData.afterImages.length === 0) {
      return {
        statusCode: 400,
        headers: { 'Access-Control-Allow-Origin': '*' },
        body: JSON.stringify({
          success: false,
          error: 'At least one before image and one after image are required'
        })
      };
    }

    // Add metadata
    projectData.id = Date.now();
    projectData.uploadDate = new Date().toISOString();
    projectData.status = 'pending_review';

    // Here you could save to a database, send email notification, etc.
    // For now, we'll just return the data
    
    console.log('Portfolio submission received:', {
      title: projectData.title,
      type: projectData.type,
      beforeImages: projectData.beforeImages.length,
      afterImages: projectData.afterImages.length,
      videos: projectData.videos.length
    });

    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type'
      },
      body: JSON.stringify({
        success: true,
        message: 'Portfolio uploaded successfully! Your submission will be reviewed and added to the portfolio.',
        data: {
          id: projectData.id,
          title: projectData.title,
          type: projectData.type,
          uploadDate: projectData.uploadDate,
          fileCount: {
            beforeImages: projectData.beforeImages.length,
            afterImages: projectData.afterImages.length,
            videos: projectData.videos.length
          }
        }
      })
    };

  } catch (error) {
    console.error('Upload error:', error);
    return {
      statusCode: 500,
      headers: { 'Access-Control-Allow-Origin': '*' },
      body: JSON.stringify({
        success: false,
        error: error.message || 'Internal server error'
      })
    };
  }
};
