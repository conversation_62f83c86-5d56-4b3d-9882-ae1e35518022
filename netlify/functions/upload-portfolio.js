const fetch = require('node-fetch');
const FormData = require('form-data');

exports.handler = async (event, context) => {
  // Only allow POST requests
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  try {
    // Parse the multipart form data
    const boundary = event.headers['content-type'].split('boundary=')[1];
    const body = Buffer.from(event.body, 'base64');
    
    // You'll need to add your GHL API credentials here
    const GHL_API_KEY = process.env.GHL_API_KEY;
    const GHL_LOCATION_ID = process.env.GHL_LOCATION_ID;
    
    if (!GHL_API_KEY || !GHL_LOCATION_ID) {
      return {
        statusCode: 500,
        body: JSON.stringify({ error: 'Missing GHL API credentials' })
      };
    }

    // Parse form data (you'll need a multipart parser)
    // For now, this is a basic structure - you'd need to implement proper parsing
    
    const uploadedFiles = [];
    
    // Example of uploading to GHL Media Library
    const uploadToGHL = async (fileBuffer, fileName, mimeType) => {
      const formData = new FormData();
      formData.append('file', fileBuffer, {
        filename: fileName,
        contentType: mimeType
      });
      
      const response = await fetch(`https://services.leadconnectorhq.com/medias/upload-file`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${GHL_API_KEY}`,
          'Version': '2021-07-28'
        },
        body: formData
      });
      
      if (!response.ok) {
        throw new Error(`GHL upload failed: ${response.statusText}`);
      }
      
      return await response.json();
    };

    // Process uploaded files and upload to GHL
    // This is where you'd parse the multipart data and extract files
    
    // Save project data to a database or file
    const projectData = {
      title: 'Project Title', // Extract from form
      description: 'Project Description', // Extract from form
      type: 'Project Type', // Extract from form
      beforeImages: [], // URLs from GHL uploads
      afterImages: [], // URLs from GHL uploads
      videos: [], // URLs from GHL uploads
      uploadDate: new Date().toISOString()
    };

    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'POST, OPTIONS'
      },
      body: JSON.stringify({
        success: true,
        message: 'Portfolio uploaded successfully',
        data: projectData
      })
    };

  } catch (error) {
    console.error('Upload error:', error);
    return {
      statusCode: 500,
      headers: {
        'Access-Control-Allow-Origin': '*'
      },
      body: JSON.stringify({
        success: false,
        error: error.message
      })
    };
  }
};
