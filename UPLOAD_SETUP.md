# Portfolio Upload Setup Guide

## Option 1: Cloudinary Integration (Recommended for Easy Setup)

### Step 1: Create Cloudinary Account
1. Go to [cloudinary.com](https://cloudinary.com) and sign up for free
2. Get your credentials from the dashboard:
   - Cloud Name
   - API Key  
   - API Secret

### Step 2: Configure Netlify Environment Variables
In your Netlify dashboard:
1. Go to Site Settings → Environment Variables
2. Add these variables:
   ```
   CLOUDINARY_CLOUD_NAME=your_cloud_name
   CLOUDINARY_API_KEY=your_api_key
   CLOUDINARY_API_SECRET=your_api_secret
   ```

### Step 3: Deploy
1. Push the code to your repository
2. <PERSON>lify will automatically deploy the functions
3. Your upload form will be available at `/upload.html`

---

## Option 2: GoHighLevel Integration

### Step 1: Get GHL API Credentials
1. In your GHL subaccount, go to Settings → API
2. Generate an API key
3. Note your Location ID

### Step 2: Configure Environment Variables
```
GHL_API_KEY=your_api_key
GHL_LOCATION_ID=your_location_id
```

### Step 3: Update Function
Use the `upload-portfolio.js` function instead of the Cloudinary one.

---

## How It Works

### Upload Process:
1. User fills out the form at `/upload.html`
2. Files are uploaded to Cloudinary (or GHL)
3. Project data is processed by Netlify function
4. Success/error message is shown to user

### File Organization:
- **Before Images**: Stored in `portfolio/before/` folder
- **After Images**: Stored in `portfolio/after/` folder  
- **Videos**: Stored in `portfolio/videos/` folder

### Features:
- ✅ Drag & drop file upload
- ✅ Image preview before upload
- ✅ File type validation
- ✅ Progress indication
- ✅ Error handling
- ✅ Responsive design

---

## Next Steps

### 1. Data Storage
Currently, the uploaded data is just logged. You may want to:
- Save to a database (Airtable, Google Sheets, etc.)
- Send email notifications
- Create a review/approval system

### 2. Integration with Portfolio
- Automatically add approved projects to your portfolio
- Create an admin interface to manage submissions
- Set up automated image optimization

### 3. Enhanced Features
- Email notifications when projects are uploaded
- Admin dashboard to review submissions
- Automatic thumbnail generation
- SEO optimization for new portfolio items

---

## Troubleshooting

### Common Issues:
1. **CORS Errors**: Make sure environment variables are set correctly
2. **File Size Limits**: Cloudinary free tier has upload limits
3. **Function Timeouts**: Large files may timeout (increase function timeout in Netlify)

### Testing:
1. Test locally with `netlify dev`
2. Check function logs in Netlify dashboard
3. Verify environment variables are set

---

## Cost Considerations

### Cloudinary (Recommended):
- **Free Tier**: 25GB storage, 25GB bandwidth/month
- **Paid Plans**: Start at $89/month for more storage
- **Benefits**: Automatic optimization, CDN, transformations

### GoHighLevel:
- **Pros**: You're already using it
- **Cons**: May have API rate limits
- **Cost**: Included in your existing GHL subscription

### Netlify Functions:
- **Free Tier**: 125K requests/month
- **Paid**: $25/month for 2M requests
- **Benefits**: Serverless, automatic scaling
