import React from 'react';
import { createRoot } from 'react-dom/client';
import QuoteForm from './components/QuoteForm';

// Mount the QuoteForm component to the DOM
const mountQuoteForm = () => {
  const container = document.getElementById('quote-form-root');
  if (container) {
    const root = createRoot(container);
    root.render(<QuoteForm />);
  }
};

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', mountQuoteForm);
} else {
  mountQuoteForm();
}

export default mountQuoteForm;
