import React from 'react';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import Layout from './components/Layout';
import Header from './components/Header';
import HomePage from './pages/HomePage';
import ServicesPage from './pages/ServicesPage';
import PortfolioPage from './pages/PortfolioPage';
import TeamPage from './pages/TeamPage';
import VideosPage from './pages/VideosPage';

// Import existing components that will be used in the home page
import Contact from './components/Contact';
import Emergency from './components/Emergency';
import Reviews from './components/Reviews';

function App() {
  return (
    <BrowserRouter>
      <div className="App">
        <Header />
        <Routes>
          <Route path="/" element={
            <Layout>
              <HomePage />
              {/* Add the existing components to the home page */}
              <Reviews />
              <section id="user-types" className="py-20 bg-gray-50">
                <div className="container mx-auto px-4 md:px-6">
                  <div className="text-center mb-12">
                    <h2 className="text-3xl md:text-4xl font-bold text-primary mb-4">
                      Specialized Services for Your Specific Needs
                    </h2>
                    <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                      We understand that different clients have different requirements.
                      That's why we offer tailored services for each of our client types.
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                    {/* Property Owners */}
                    <div className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                      <div
                        className="h-48 bg-cover bg-center"
                        style={{
                          backgroundImage: "url('https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af76b97b2a314fdc20a.webp')"
                        }}
                      >
                        <div className="h-full w-full bg-primary/50 flex items-center justify-center">
                          <h3 className="text-2xl font-bold text-white">Property Owners</h3>
                        </div>
                      </div>
                      <div className="p-6">
                        <ul className="space-y-3 mb-6">
                          <li className="flex items-start">
                            <svg className="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                              <polyline points="22 4 12 14.01 9 11.01"></polyline>
                            </svg>
                            <span>Expert diagnosis of home issues</span>
                          </li>
                          <li className="flex items-start">
                            <svg className="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                              <polyline points="22 4 12 14.01 9 11.01"></polyline>
                            </svg>
                            <span>Comprehensive home maintenance</span>
                          </li>
                          <li className="flex items-start">
                            <svg className="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                              <polyline points="22 4 12 14.01 9 11.01"></polyline>
                            </svg>
                            <span>Quality remodeling and repairs</span>
                          </li>
                          <li className="flex items-start">
                            <svg className="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                              <polyline points="22 4 12 14.01 9 11.01"></polyline>
                            </svg>
                            <span>Skilled craftsmanship you can trust</span>
                          </li>
                        </ul>
                        <p className="text-gray-600 mb-6">
                          We're passionate about diagnosing issues, maintaining homes, and
                          delivering quality remodeling and repairs. Our skilled team
                          enjoys the work and takes pride in every project.
                        </p>
                      </div>
                    </div>

                    {/* Real Estate Agents */}
                    <div className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                      <div
                        className="h-48 bg-cover bg-center"
                        style={{
                          backgroundImage: "url('https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af742a253c12ce1cdb6.webp')"
                        }}
                      >
                        <div className="h-full w-full bg-primary/50 flex items-center justify-center">
                          <h3 className="text-2xl font-bold text-white">Real Estate Agents</h3>
                        </div>
                      </div>
                      <div className="p-6">
                        <ul className="space-y-3 mb-6">
                          <li className="flex items-start">
                            <svg className="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                              <polyline points="22 4 12 14.01 9 11.01"></polyline>
                            </svg>
                            <span>Quick handling of inspection items</span>
                          </li>
                          <li className="flex items-start">
                            <svg className="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                              <polyline points="22 4 12 14.01 9 11.01"></polyline>
                            </svg>
                            <span>Prompt response to addenda requests</span>
                          </li>
                          <li className="flex items-start">
                            <svg className="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                              <polyline points="22 4 12 14.01 9 11.01"></polyline>
                            </svg>
                            <span>Licensed, bonded & insured work</span>
                          </li>
                          <li className="flex items-start">
                            <svg className="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                              <polyline points="22 4 12 14.01 9 11.01"></polyline>
                            </svg>
                            <span>Guaranteed tight deadlines</span>
                          </li>
                        </ul>
                        <p className="text-gray-600 mb-6">
                          We handle inspection items and addenda quickly with licensed,
                          bonded, and insured plumbing and structural work.
                          Our licensed plumber on staff ensures quality work.
                        </p>
                      </div>
                    </div>

                    {/* Property Managers */}
                    <div className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                      <div
                        className="h-48 bg-cover bg-center"
                        style={{
                          backgroundImage: "url('https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af74a13d59a2324f42a.webp')"
                        }}
                      >
                        <div className="h-full w-full bg-primary/50 flex items-center justify-center">
                          <h3 className="text-2xl font-bold text-white">Property Managers</h3>
                        </div>
                      </div>
                      <div className="p-6">
                        <ul className="space-y-3 mb-6">
                          <li className="flex items-start">
                            <svg className="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                              <polyline points="22 4 12 14.01 9 11.01"></polyline>
                            </svg>
                            <span>Efficient make-ready services</span>
                          </li>
                          <li className="flex items-start">
                            <svg className="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                              <polyline points="22 4 12 14.01 9 11.01"></polyline>
                            </svg>
                            <span>Responsive tenant repair services</span>
                          </li>
                          <li className="flex items-start">
                            <svg className="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                              <polyline points="22 4 12 14.01 9 11.01"></polyline>
                            </svg>
                            <span>Expert coordination of services</span>
                          </li>
                          <li className="flex items-start">
                            <svg className="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                              <polyline points="22 4 12 14.01 9 11.01"></polyline>
                            </svg>
                            <span>Complete repairs from foundation to attic</span>
                          </li>
                        </ul>
                        <p className="text-gray-600 mb-6">
                          We provide make-readies, tenant repairs, and expert coordination
                          for all your rental property needs. Our comprehensive services
                          cover everything from foundation to attic.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </section>
              <Emergency />
              <Contact />
            </Layout>
          } />
          <Route path="/services" element={<Layout><ServicesPage /></Layout>} />
          <Route path="/portfolio" element={<Layout><PortfolioPage /></Layout>} />
          <Route path="/team" element={<Layout><TeamPage /></Layout>} />
          <Route path="/videos" element={<Layout><VideosPage /></Layout>} />
        </Routes>
      </div>
    </BrowserRouter>
  );
}

export default App;
