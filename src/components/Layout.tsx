import React from 'react';
import { Phone, Mail, MapPin } from 'lucide-react';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen flex flex-col">
      {/* Header will be added here when we update it for SPA navigation */}
      
      {/* Main Content */}
      <main className="flex-grow">
        {children}
      </main>

      {/* Footer */}
      <footer className="bg-primary text-white py-12 w-full">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid md:grid-cols-4 gap-8">
            {/* Company Info */}
            <div className="md:col-span-2">
              <div className="mb-6">
                <img
                  src="https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/681439e6dde4a49484f17fe1.webp"
                  alt="Essential Property Services Logo"
                  className="h-16 w-auto brightness-0 invert"
                />
              </div>
              <p className="text-gray-200 mb-6 leading-relaxed max-w-md">
                Serving people and property with wisdom and humility. Your trusted partner for all property maintenance, repairs, and remodeling needs in Anchorage, Alaska.
              </p>
              <div className="flex space-x-4">
                <a
                  href="tel:9076009900"
                  className="w-10 h-10 bg-accent rounded-lg flex items-center justify-center text-primary hover:bg-accent/90 transition-colors"
                >
                  <Phone className="w-5 h-5" />
                </a>
                <a
                  href="mailto:<EMAIL>"
                  className="w-10 h-10 bg-accent rounded-lg flex items-center justify-center text-primary hover:bg-accent/90 transition-colors"
                >
                  <Mail className="w-5 h-5" />
                </a>
              </div>
            </div>

            {/* Services */}
            <div>
              <h3 className="text-lg font-semibold text-accent mb-4">Services</h3>
              <ul className="space-y-2 text-gray-200">
                <li><a href="/services" className="hover:text-accent transition-colors">Kitchen & Bath</a></li>
                <li><a href="/services" className="hover:text-accent transition-colors">Interior Improvements</a></li>
                <li><a href="/services" className="hover:text-accent transition-colors">Exterior & Structural</a></li>
                <li><a href="/services" className="hover:text-accent transition-colors">Emergency Services</a></li>
                <li><a href="/services" className="hover:text-accent transition-colors">Maintenance</a></li>
                <li><a href="/services" className="hover:text-accent transition-colors">Cleaning Services</a></li>
              </ul>
            </div>

            {/* Contact */}
            <div>
              <h3 className="text-lg font-semibold text-accent mb-4">Contact Info</h3>
              <div className="space-y-3 text-gray-200">
                <div className="flex items-center">
                  <Phone className="w-5 h-5 mr-3 text-accent" />
                  <div>
                    <a href="tel:9076009900" className="hover:text-accent transition-colors block">
                      (*************
                    </a>
                    <span className="text-sm text-gray-300">24/7 Emergency</span>
                  </div>
                </div>
                <div className="flex items-center">
                  <Mail className="w-5 h-5 mr-3 text-accent" />
                  <a href="mailto:<EMAIL>" className="hover:text-accent transition-colors">
                    <EMAIL>
                  </a>
                </div>
                <div className="flex items-center">
                  <MapPin className="w-5 h-5 mr-3 text-accent" />
                  <span>Serving Anchorage, Alaska</span>
                </div>
              </div>
            </div>
          </div>

          {/* Client Types */}
          <div className="mt-12 pt-8 border-t border-primary/20">
            <h3 className="text-lg font-semibold text-accent mb-4 text-center">We Serve</h3>
            <div className="grid md:grid-cols-3 gap-6 text-center">
              <div>
                <h4 className="font-semibold text-white mb-2">Property Owners</h4>
                <p className="text-gray-300 text-sm">Expert diagnosis, maintenance, and quality remodeling for your home</p>
              </div>
              <div>
                <h4 className="font-semibold text-white mb-2">Real Estate Agents</h4>
                <p className="text-gray-300 text-sm">Quick inspection items, licensed work, guaranteed deadlines</p>
              </div>
              <div>
                <h4 className="font-semibold text-white mb-2">Property Managers</h4>
                <p className="text-gray-300 text-sm">Make-readies, tenant repairs, complete property solutions</p>
              </div>
            </div>
          </div>

          {/* Bottom Bar */}
          <div className="border-t border-primary/20 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-300 text-sm">
              © 2025 Essential Property Services. All rights reserved.
            </p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a href="/privacy" className="text-gray-300 hover:text-accent text-sm transition-colors">
                Privacy Policy
              </a>
              <a href="/terms" className="text-gray-300 hover:text-accent text-sm transition-colors">
                Terms of Service
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Layout;
