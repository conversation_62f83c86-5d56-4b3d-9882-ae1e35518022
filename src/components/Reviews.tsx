import React, { useEffect } from 'react';
import { Star } from 'lucide-react';

const Reviews = () => {
  useEffect(() => {
    // Load the review widget script
    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.src = 'https://reputationhub.site/reputation/assets/review-widget.js';
    script.async = true;
    document.head.appendChild(script);

    return () => {
      // Cleanup script on unmount
      const existingScript = document.querySelector('script[src="https://reputationhub.site/reputation/assets/review-widget.js"]');
      if (existingScript) {
        document.head.removeChild(existingScript);
      }
    };
  }, []);

  return (
    <section id="reviews" className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-primary mb-4">
            What Our Customers Say
          </h2>
          <div className="flex items-center justify-center mb-4">
            <div className="flex items-center">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className="w-6 h-6 text-accent fill-current" />
              ))}
            </div>
            <span className="ml-2 text-xl font-semibold text-gray-700">5.0</span>
            <span className="ml-2 text-gray-600">• 28+ Google Reviews</span>
          </div>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Don't just take our word for it. See what our satisfied customers have to say about our work.
          </p>
        </div>

        {/* Google Reviews Widget */}
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-lg shadow-lg p-6">
            <iframe 
              className="lc_reviews_widget" 
              src="https://reputationhub.site/reputation/widgets/review_widget/ZvYXEcu0DmkCtuv5cGcG" 
              frameBorder="0" 
              scrolling="no" 
              style={{
                minWidth: '100%', 
                width: '100%',
                minHeight: '400px',
                border: 'none'
              }}
              title="Google Reviews"
            />
          </div>
        </div>

        {/* Call to Action */}
        <div className="mt-12 text-center">
          <p className="text-lg text-gray-600 mb-6">
            Ready to experience the Essential Property Services difference?
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="tel:9076009900"
              className="inline-flex items-center px-8 py-4 bg-primary hover:bg-primary/90 text-white font-semibold rounded-md transition-colors"
            >
              Call Now: (*************
            </a>
            <a
              href="#contact"
              className="inline-flex items-center px-8 py-4 bg-accent hover:bg-accent/90 text-primary font-semibold rounded-md transition-colors"
            >
              Get Your Free Estimate
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Reviews;
