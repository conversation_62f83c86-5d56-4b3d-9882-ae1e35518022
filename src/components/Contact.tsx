import React from 'react';
import { Phone, Mail, MapPin, Clock } from 'lucide-react';
import QuoteForm from './QuoteForm';

const Contact = () => {
  const contactInfo = [
    {
      icon: Phone,
      label: 'Call Us Directly',
      value: '(*************',
      subtitle: 'Fastest response for urgent needs',
      href: 'tel:**********',
      iconColor: 'bg-primary'
    },
    {
      icon: Mail,
      label: 'Email Us',
      value: '<EMAIL>',
      subtitle: 'For detailed inquiries and quotes',
      href: 'mailto:<EMAIL>',
      iconColor: 'bg-primary'
    },
    {
      icon: Clock,
      label: 'Hours',
      value: '24/7/364',
      subtitle: 'Emergency services always available',
      href: null,
      iconColor: 'bg-primary'
    }
  ];

  const credentials = [
    'Alaska General Contractor License 217482',
    'MOA License CON14200',
    'Fully Insured and Bonded'
  ];

  return (
    <section id="contact" className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Let Us Serve Your Property Needs Today
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto">
            Whether you're a property owner, real estate agent, or property manager, we're here to
            serve you with excellence, humility, and professionalism.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 max-w-7xl mx-auto">
          {/* Contact Information */}
          <div className="bg-white rounded-lg shadow-lg p-8">
            <h3 className="text-2xl font-bold text-primary mb-8">Contact Us</h3>

            <div className="space-y-6 mb-8">
              {contactInfo.map((info, index) => {
                const Icon = info.icon;
                const content = (
                  <div className="flex items-start space-x-4">
                    <div className={`w-12 h-12 rounded-lg ${info.iconColor} flex items-center justify-center flex-shrink-0`}>
                      <Icon className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <div className="text-lg font-semibold text-gray-900 mb-1">{info.label}</div>
                      <div className="text-lg text-gray-900 font-medium">{info.value}</div>
                      {info.subtitle && (
                        <div className="text-sm text-gray-600">{info.subtitle}</div>
                      )}
                    </div>
                  </div>
                );

                return info.href ? (
                  <a key={index} href={info.href} className="block hover:bg-gray-50 p-2 rounded-lg transition-colors">
                    {content}
                  </a>
                ) : (
                  <div key={index} className="p-2">
                    {content}
                  </div>
                );
              })}
            </div>

            {/* Professional Credentials */}
            <div className="border-t pt-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">Professional Credentials</h4>
              <ul className="space-y-2">
                {credentials.map((credential, index) => (
                  <li key={index} className="flex items-center text-gray-700">
                    <div className="w-2 h-2 rounded-full bg-accent mr-3"></div>
                    {credential}
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Quote Form */}
          <div id="quote-form">
            <QuoteForm />
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;