import React from 'react';

const About = () => {
  const values = [
    {
      title: 'Licensed & Insured',
      description: 'Fully licensed, bonded, and insured for your peace of mind. Our licensed plumber on staff ensures quality work.'
    },
    {
      title: 'Reliable Service',
      description: 'We show up when we say we will and complete projects on time. Dependability is at the core of our business.'
    },
    {
      title: 'Quality Craftsmanship',
      description: 'Our skilled team takes pride in every project, delivering quality remodeling and repairs with attention to detail.'
    },
    {
      title: 'Honest Communication',
      description: 'Transparent pricing, clear timelines, and honest assessments. We keep you informed throughout every project.'
    }
  ];

  return (
    <section id="about" className="py-20 bg-gradient-to-br from-slate-800 via-gray-900 to-slate-900 relative">
      {/* Background Effects */}
      <div className="absolute inset-0 opacity-8">
        <div className="absolute top-1/3 right-1/3 w-80 h-80 bg-orange-500/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/3 left-1/3 w-80 h-80 bg-green-500/20 rounded-full blur-3xl"></div>
      </div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Content */}
          <div className="select-text">
            <h2 className="text-4xl font-bold text-white mb-6">
              Why Choose Essential Property Services?
            </h2>

            <p className="text-xl text-gray-300 mb-8 leading-relaxed">
              Essential Property Services is your trusted partner for all property maintenance and remodeling needs.
              With years of experience and a commitment to excellence, we provide reliable, quality services that
              keep your property in top condition.
            </p>

            <div className="space-y-6">
              {values.map((value, index) => (
                <div key={index} className="flex items-start space-x-4">
                  <div className="w-2 h-2 rounded-full bg-yellow-400 mt-3 flex-shrink-0"></div>
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-2">{value.title}</h3>
                    <p className="text-gray-300">{value.description}</p>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-8">
              <button
                onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
                className="brand-button text-gray-900 px-8 py-3 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg shadow-yellow-500/25"
              >
                Get Your Free Estimate
              </button>
            </div>
          </div>

          {/* Images Column */}
          <div className="space-y-6">
            <div className="relative rounded-2xl overflow-hidden shadow-xl">
              <img
                src="/bathroom-remodel-1.jpg"
                alt="Professional Bathroom Remodeling"
                className="w-full h-64 object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-slate-900/40 to-transparent"></div>
            </div>

            <div className="relative rounded-2xl overflow-hidden shadow-xl">
              <img
                src="/kitchen-remodel-1.jpg"
                alt="Quality Kitchen Renovation"
                className="w-full h-64 object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-slate-900/40 to-transparent"></div>
            </div>
          </div>
        </div>
        {/* Value Proposition */}
        <div className="mt-20 text-center">
          <div className="bg-gradient-to-br from-slate-800/60 to-gray-900/60 backdrop-blur-sm border border-slate-700/50 rounded-2xl p-8 lg:p-12">
            <h3 className="text-3xl font-bold text-white mb-6">
              The Essential Property Services Difference
            </h3>
            <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed mb-6">
              What sets Essential Property Services apart is our commitment to building lasting relationships
              with our clients through honest communication, reliable service, and quality workmanship. We
              understand that your property is one of your most important investments.
            </p>
            <p className="text-lg text-gray-300 max-w-4xl mx-auto leading-relaxed">
              Our experienced team approaches every project with professionalism and attention to detail,
              ensuring your complete satisfaction. From small repairs to major remodels, we're here to
              help maintain and improve your property.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;