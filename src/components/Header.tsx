import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Menu, X, Phone, Calendar } from 'lucide-react';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const location = useLocation();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const closeMenu = () => setIsMenuOpen(false);

  return (
    <header
      className={`fixed w-full z-50 transition-all duration-300 ${
        isScrolled ? 'py-4' : 'py-6'
      }`}
    >
      <div className="container mx-auto px-4 md:px-6">
        {/* Mobile: Small floating menu on right */}
        <div className="lg:hidden flex justify-end">
          <div
            className={`backdrop-blur-sm rounded-2xl px-4 py-3 transition-all duration-300 w-auto ${
              isScrolled ? 'bg-white/95 shadow-lg' : ''
            }`}
          >
            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className={`${isScrolled ? 'text-primary' : 'text-white'}`}
            >
              {isMenuOpen ? <X className="w-7 h-7" /> : <Menu className="w-7 h-7" />}
            </button>
          </div>
        </div>

        {/* Desktop: Full width navigation */}
        <div
          className={`hidden lg:block backdrop-blur-sm rounded-2xl px-6 py-4 transition-all duration-300 ${
            isScrolled ? 'bg-white/95 shadow-lg' : ''
          }`}
        >
          <div className="flex justify-center items-center">
            {/* Left Navigation */}
            <div className="flex items-center space-x-8 mr-auto">
              <nav>
                <ul className="flex space-x-8">
                  <li>
                    <Link
                      to="/services"
                      className={`font-bold hover:text-accent transition text-lg ${
                        isScrolled ? 'text-gray-800 hover:text-primary' : 'text-white hover:text-accent'
                      }`}
                    >
                      Services
                    </Link>
                  </li>
                  <li>
                    <Link
                      to="/portfolio"
                      className={`font-bold hover:text-accent transition text-lg ${
                        isScrolled ? 'text-gray-800 hover:text-primary' : 'text-white hover:text-accent'
                      }`}
                    >
                      Portfolio
                    </Link>
                  </li>
                  <li>
                    <Link
                      to="/team"
                      className={`font-bold hover:text-accent transition text-lg ${
                        isScrolled ? 'text-gray-800 hover:text-primary' : 'text-white hover:text-accent'
                      }`}
                    >
                      Our Team
                    </Link>
                  </li>
                  <li>
                    <Link
                      to="/videos"
                      className={`font-bold hover:text-accent transition text-lg ${
                        isScrolled ? 'text-gray-800 hover:text-primary' : 'text-white hover:text-accent'
                      }`}
                    >
                      Videos
                    </Link>
                  </li>
                </ul>
              </nav>
            </div>

            {/* Right Navigation */}
            <div className="flex items-center space-x-4 ml-auto">
              {/* Emergency Call Button */}
              <a
                href="tel:9076009900"
                className="flex items-center bg-primary hover:bg-primary/90 text-white px-4 py-3 rounded-md font-bold transition text-sm"
              >
                <Phone className="w-[18px] h-[18px] mr-2" />
                Emergency 24/7
              </a>

              {/* Maintenance Button */}
              <a
                href="#contact"
                className={`font-bold hover:bg-white/90 transition text-sm px-4 py-3 rounded-md ${
                  isScrolled ? 'text-primary bg-gray-100 hover:bg-gray-200' : 'bg-white text-primary'
                }`}
              >
                Maintenance
              </a>

              {/* Estimate Button */}
              <a
                href="#scheduling"
                className="flex items-center bg-accent hover:bg-accent/90 text-primary px-4 py-3 rounded-md font-bold transition text-sm"
              >
                <Calendar className="w-[18px] h-[18px] mr-2" />
                Estimate
              </a>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isMenuOpen && (
        <div className="lg:hidden bg-white/95 backdrop-blur-sm shadow-lg fixed top-0 left-0 right-0 z-50 mt-20 max-h-[calc(100vh-5rem)] overflow-y-auto rounded-b-2xl mx-4">
          <nav className="px-6 py-6">
            <ul className="space-y-4">
              <li>
                <Link
                  to="/services"
                  className="block py-3 text-gray-800 font-medium hover:text-primary text-lg"
                  onClick={closeMenu}
                >
                  Services
                </Link>
              </li>
              <li>
                <Link
                  to="/portfolio"
                  className="block py-3 text-gray-800 font-medium hover:text-primary text-lg"
                  onClick={closeMenu}
                >
                  Portfolio
                </Link>
              </li>
              <li>
                <Link
                  to="/team"
                  className="block py-3 text-gray-800 font-medium hover:text-primary text-lg"
                  onClick={closeMenu}
                >
                  Our Team
                </Link>
              </li>
              <li>
                <Link
                  to="/videos"
                  className="block py-3 text-gray-800 font-medium hover:text-primary text-lg"
                  onClick={closeMenu}
                >
                  Videos
                </Link>
              </li>
              <li>
                <a
                  href="#contact"
                  className="block py-3 text-gray-800 font-medium hover:text-primary text-lg"
                  onClick={closeMenu}
                >
                  Maintenance
                </a>
              </li>
              {/* Emergency Call Button */}
              <li className="pt-4">
                <a
                  href="tel:9076009900"
                  className="flex items-center w-full bg-primary hover:bg-primary/90 text-white px-4 py-3 rounded-md font-bold justify-center transition"
                  onClick={closeMenu}
                >
                  <Phone className="w-[18px] h-[18px] mr-2" />
                  Emergency 24/7
                </a>
              </li>
              {/* Estimate Button */}
              <li>
                <a
                  href="#scheduling"
                  className="flex items-center w-full bg-accent hover:bg-accent/90 text-primary px-4 py-3 rounded-md font-bold justify-center transition mt-2"
                  onClick={closeMenu}
                >
                  <Calendar className="w-[18px] h-[18px] mr-2" />
                  Get Estimate
                </a>
              </li>
            </ul>
          </nav>
        </div>
      )}
    </header>
  );
};

export default Header;