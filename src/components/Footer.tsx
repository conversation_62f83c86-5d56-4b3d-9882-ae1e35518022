import React from 'react';
import { Phone, Mail, Globe } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-primary text-white py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid md:grid-cols-3 gap-8">
          {/* Company Info */}
          <div>
            <div className="flex items-center mb-4">
              <h3 className="text-xl font-bold text-white">Essential Property Services</h3>
            </div>
            <p className="text-white/80 mb-4 leading-relaxed">
              Professional property maintenance and remodeling services in Alaska.
              Your property is our priority.
            </p>
            <div className="flex space-x-4">
              <a
                href="tel:9076009900"
                className="w-10 h-10 bg-accent/20 border border-accent/30 rounded-lg flex items-center justify-center text-white hover:bg-accent hover:text-primary transition-colors"
              >
                <Phone className="w-5 h-5" />
              </a>
              <a
                href="mailto:<EMAIL>"
                className="w-10 h-10 bg-accent/20 border border-accent/30 rounded-lg flex items-center justify-center text-white hover:bg-accent hover:text-primary transition-colors"
              >
                <Mail className="w-5 h-5" />
              </a>
              <a
                href="https://epsak.com"
                target="_blank"
                rel="noopener noreferrer"
                className="w-10 h-10 bg-accent/20 border border-accent/30 rounded-lg flex items-center justify-center text-white hover:bg-accent hover:text-primary transition-colors"
              >
                <Globe className="w-5 h-5" />
              </a>
            </div>
          </div>

          {/* Services */}
          <div>
            <h3 className="text-lg font-semibold text-white mb-4">Services</h3>
            <ul className="space-y-2 text-white/80">
              <li><a href="/services" className="hover:text-accent transition-colors">Kitchen Remodeling</a></li>
              <li><a href="/services" className="hover:text-accent transition-colors">Bathroom Renovation</a></li>
              <li><a href="/portfolio" className="hover:text-accent transition-colors">Project Gallery</a></li>
              <li><a href="/services" className="hover:text-accent transition-colors">Plumbing Services</a></li>
              <li><a href="#emergency" className="hover:text-accent transition-colors">Emergency Repairs</a></li>
              <li><a href="/services" className="hover:text-accent transition-colors">Property Maintenance</a></li>
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h3 className="text-lg font-semibold text-white mb-4">Contact Info</h3>
            <div className="space-y-3 text-white/80">
              <div className="flex items-center">
                <Phone className="w-5 h-5 mr-3 text-accent" />
                <a href="tel:9076009900" className="hover:text-accent transition-colors">
                  (*************
                </a>
              </div>
              <div className="flex items-center">
                <Mail className="w-5 h-5 mr-3 text-accent" />
                <a href="mailto:<EMAIL>" className="hover:text-accent transition-colors">
                  <EMAIL>
                </a>
              </div>
              <div className="flex items-center">
                <Globe className="w-5 h-5 mr-3 text-accent" />
                <a href="https://epsak.com" target="_blank" rel="noopener noreferrer" className="hover:text-accent transition-colors">
                  epsak.com
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-white/20 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-white/60 text-sm">
            © 2025 Essential Property Services LLC. All rights reserved.
          </p>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <a href="/privacy-policy.html" className="text-white/60 hover:text-accent text-sm transition-colors">
              Privacy Policy
            </a>
            <a href="/terms-of-service.html" className="text-white/60 hover:text-accent text-sm transition-colors">
              Terms of Service
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;