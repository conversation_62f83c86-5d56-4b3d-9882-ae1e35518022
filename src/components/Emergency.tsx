import React, { useEffect } from 'react';
import { Clock, Phone, CheckCircle } from 'lucide-react';

const Emergency = () => {
  useEffect(() => {
    // Load HighLevel form embed script
    const script = document.createElement('script');
    script.src = 'https://link.msgsndr.com/js/form_embed.js';
    script.type = 'text/javascript';
    document.head.appendChild(script);

    return () => {
      // Cleanup script on unmount
      const existingScript = document.querySelector('script[src="https://link.msgsndr.com/js/form_embed.js"]');
      if (existingScript) {
        document.head.removeChild(existingScript);
      }
    };
  }, []);

  const emergencyServices = [
    'Plumbing emergencies (leaks, floods, no water)',
    'Structural damage (storm, accident, fence repair)',
    'Heating/cooling failures in extreme weather'
  ];

  return (
    <section id="emergency" className="py-20 bg-primary text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-10">
          <h2 className="text-3xl md:text-5xl font-bold mb-4">
            <span className="text-accent">24/7</span> Emergency Services
          </h2>
        </div>

        <div className="mb-12">
          <div className="flex flex-col gap-12 items-center">
            {/* Emergency Services Section */}
            <div className="bg-primary text-white rounded-xl p-8 shadow-lg w-full border border-white/20">
              <div className="flex items-center mb-6">
                <div className="bg-accent rounded-full p-3 mr-4">
                  <Clock className="w-8 h-8 text-primary" />
                </div>
                <h3 className="text-2xl font-bold">Have an Emergency?</h3>
              </div>

              <ul className="space-y-4 mb-8">
                {emergencyServices.map((service, index) => (
                  <li key={index} className="flex items-start">
                    <CheckCircle className="w-5 h-5 text-accent mt-1 mr-3 flex-shrink-0" />
                    <span>{service}</span>
                  </li>
                ))}
              </ul>

              <div className="flex flex-col sm:flex-row gap-4">
                <a
                  href="tel:9076009900"
                  className="flex items-center justify-center bg-accent text-primary hover:bg-accent/90 px-6 py-4 rounded-lg text-xl font-semibold transition transform hover:scale-105 whitespace-nowrap"
                >
                  <Phone className="w-6 h-6 mr-2" />
                  Call: (*************
                </a>
                <a
                  href="#contact"
                  className="flex items-center justify-center bg-white/20 hover:bg-white/30 px-6 py-4 rounded-lg text-xl font-semibold transition"
                >
                  Request Emergency Service
                </a>
              </div>
            </div>

            {/* Scheduling Section */}
            <div
              id="scheduling"
              className="bg-white rounded-xl shadow-xl overflow-hidden w-full"
            >
              <div className="p-6 bg-accent text-primary font-bold text-xl text-center">
                Schedule Your Service
              </div>
              <div
                className="booking-container"
                style={{ height: '800px', overflow: 'hidden' }}
              >
                <iframe
                  src="https://api.leadconnectorhq.com/widget/booking/NuS5BqNCvLPpRUBBllGn"
                  style={{
                    width: '100%',
                    height: '100%',
                    border: 'none',
                    overflow: 'hidden',
                  }}
                  scrolling="no"
                  id="NuS5BqNCvLPpRUBBllGn_1747152639241"
                  title="Schedule Your Service"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Emergency;
