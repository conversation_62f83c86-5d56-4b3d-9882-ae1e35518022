import React from 'react';

const HomePage = () => {
  return (
    <div>
      {/* Hero Section */}
      <section className="relative h-[100vh] md:h-[75vh] flex items-center">
        <div className="absolute inset-0">
          <img
            src="https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68506368d97df57e336772da.webp"
            alt="Beautiful Alaskan Home Project"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-black opacity-60"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            {/* Large Logo Above Headline */}
            <div className="mb-6 md:mb-8 animate-fadeIn">
              <img
                src="https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/681439e6dde4a49484f17fe1.webp"
                alt="Essential Property Services Logo"
                className="h-24 sm:h-32 md:h-48 lg:h-60 mx-auto brightness-0 invert"
                style={{
                  filter: 'brightness(0) invert(1) drop-shadow(0 0 8px rgba(0, 0, 0, 0.5))'
                }}
              />
            </div>

            <h1 className="text-4xl md:text-5xl font-bold leading-tight mb-10 animate-fadeIn text-white">
              SERVING PEOPLE AND PROPERTY WITH WISDOM AND HUMILITY
            </h1>

            {/* Three prominent CTA buttons */}
            <div className="flex flex-col md:flex-row gap-4 justify-center">
              {/* Emergency Call Button */}
              <a
                href="tel:9076009900"
                className="px-8 py-4 bg-primary hover:bg-primary/90 text-white font-bold rounded-md text-center transition flex items-center justify-center text-lg"
              >
                <svg
                  className="w-6 h-6 mr-2"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z" />
                </svg>
                Emergency Call 24/7
              </a>

              {/* Schedule Repair or Estimate Button */}
              <a
                href="tel:9076009900"
                className="px-8 py-4 bg-accent hover:bg-accent/90 text-primary font-bold rounded-md text-center transition flex items-center justify-center text-lg"
              >
                <svg
                  className="w-6 h-6 mr-2"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z" />
                </svg>
                Call for Estimate
              </a>

              {/* Schedule Service Online Button */}
              <a
                href="#scheduling"
                className="px-8 py-4 bg-white hover:bg-gray-100 text-primary font-bold rounded-md text-center transition flex items-center justify-center text-lg"
              >
                <svg
                  className="w-6 h-6 mr-2"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                  <line x1="16" y1="2" x2="16" y2="6"></line>
                  <line x1="8" y1="2" x2="8" y2="6"></line>
                  <line x1="3" y1="10" x2="21" y2="10"></line>
                </svg>
                Schedule Online
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-20 bg-primary">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-accent mb-4">
              Created to Serve:
            </h2>
            <p className="text-xl text-gray-200 max-w-3xl mx-auto">
              Intentionally pursuing our calling through serving your property
              needs with excellence.
            </p>
          </div>

          {/* Services Grid - 4 Services in a Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Emergency Services */}
            <div className="group">
              <div className="relative overflow-hidden rounded-lg shadow-lg cursor-pointer aspect-[4/3]">
                <div
                  className="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110"
                  style={{
                    backgroundImage: "url('https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af7d97df52dbc67c83e.webp')"
                  }}
                ></div>
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
                  <h3 className="text-xl font-bold">Emergency Services</h3>
                  <p className="text-sm text-accent">24/7 Availability</p>
                </div>
              </div>
              <p className="mt-2 text-gray-200 text-sm">
                Round-the-clock emergency response for urgent plumbing
                and structural issues.
              </p>
            </div>

            {/* Maintenance */}
            <div className="group">
              <div className="relative overflow-hidden rounded-lg shadow-lg cursor-pointer aspect-[4/3]">
                <div
                  className="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110"
                  style={{
                    backgroundImage: "url('https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af790fc434dcdb829c8.webp')"
                  }}
                ></div>
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
                  <h3 className="text-xl font-bold">Maintenance</h3>
                  <p className="text-sm text-accent">Preventive Care</p>
                </div>
              </div>
              <p className="mt-2 text-gray-200 text-sm">
                Regular maintenance services to keep your property in optimal
                condition year-round.
              </p>
            </div>

            {/* Remodeling */}
            <div className="group">
              <div className="relative overflow-hidden rounded-lg shadow-lg cursor-pointer aspect-[4/3]">
                <div
                  className="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110"
                  style={{
                    backgroundImage: "url('https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af7bd2be4faeaaf3744.webp')"
                  }}
                ></div>
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
                  <h3 className="text-xl font-bold">Remodeling</h3>
                  <p className="text-sm text-accent">Complete Renovations</p>
                </div>
              </div>
              <p className="mt-2 text-gray-200 text-sm">
                Transform your spaces with comprehensive remodeling services for
                kitchens, bathrooms, and more.
              </p>
            </div>

            {/* Cleaning */}
            <div className="group">
              <div className="relative overflow-hidden rounded-lg shadow-lg cursor-pointer aspect-[4/3]">
                <div
                  className="absolute inset-0 bg-cover bg-center transition-transform duration-500 group-hover:scale-110"
                  style={{
                    backgroundImage: "url('https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af790fc435f1db829c5.webp')"
                  }}
                ></div>
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
                  <h3 className="text-xl font-bold">Cleaning</h3>
                  <p className="text-sm text-accent">Professional Cleaning</p>
                </div>
              </div>
              <p className="mt-2 text-gray-200 text-sm">
                Thorough cleaning services to maintain your property's
                appearance and hygiene standards.
              </p>
            </div>
          </div>

          <div className="mt-12 text-center">
            <a
              href="#contact"
              className="inline-flex items-center px-8 py-4 bg-accent hover:bg-accent/90 text-primary font-semibold rounded-md transition-colors"
            >
              Request Your Free Estimate
            </a>
          </div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
