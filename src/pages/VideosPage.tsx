import React from 'react';

const VideosPage = () => {
  const videos = [
    {
      id: 1,
      title: "Kitchen Remodel Process",
      description: "Watch our complete kitchen transformation from start to finish",
      thumbnail: "https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af7bd2be4faeaaf3744.webp",
      videoUrl: "#", // Placeholder for actual video URL
      duration: "5:30"
    },
    {
      id: 2,
      title: "Bathroom Renovation Timelapse",
      description: "See how we transform outdated bathrooms into modern spaces",
      thumbnail: "https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af74a13d500e124f427.webp",
      videoUrl: "#", // Placeholder for actual video URL
      duration: "3:45"
    },
    {
      id: 3,
      title: "Emergency Flood Restoration",
      description: "Our rapid response team in action during emergency situations",
      thumbnail: "https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af7d97df52dbc67c83e.webp",
      videoUrl: "#", // Placeholder for actual video URL
      duration: "4:20"
    },
    {
      id: 4,
      title: "Professional Maintenance Services",
      description: "Regular maintenance keeps your property in top condition",
      thumbnail: "https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af790fc434dcdb829c8.webp",
      videoUrl: "#", // Placeholder for actual video URL
      duration: "2:15"
    }
  ];

  return (
    <div>
      {/* Hero Section */}
      <section className="relative h-[50vh] flex items-center">
        <div className="absolute inset-0">
          <img
            src="https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68506368d97df57e336772da.webp"
            alt="Essential Property Services Videos"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-black opacity-60"></div>
        </div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold leading-tight mb-6 text-white">
              Project Videos
            </h1>
            <p className="text-xl text-gray-200">
              Watch our work in action and see the transformation process
            </p>
          </div>
        </div>
      </section>

      {/* Videos Grid */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-4">
              See Our Work in Action
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Get an inside look at our processes and see the quality craftsmanship that goes into every project.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-2 gap-8">
            {videos.map((video) => (
              <div key={video.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                <div className="relative group cursor-pointer">
                  <img
                    src={video.thumbnail}
                    alt={video.title}
                    className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-40 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                    <div className="w-16 h-16 bg-white bg-opacity-90 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform">
                      <svg className="w-6 h-6 text-primary ml-1" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8 5v14l11-7z"/>
                      </svg>
                    </div>
                  </div>
                  <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white px-2 py-1 rounded text-sm">
                    {video.duration}
                  </div>
                </div>
                
                <div className="p-6">
                  <h3 className="text-xl font-bold text-primary mb-2">{video.title}</h3>
                  <p className="text-gray-600 mb-4">{video.description}</p>
                  
                  <button className="inline-flex items-center text-accent hover:text-accent/80 font-semibold transition-colors">
                    <svg className="w-4 h-4 mr-2" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M8 5v14l11-7z"/>
                    </svg>
                    Watch Video
                  </button>
                </div>
              </div>
            ))}
          </div>

          {/* Call to Action */}
          <div className="mt-16 text-center bg-white rounded-lg p-8 shadow-lg">
            <h3 className="text-2xl font-bold text-primary mb-4">Ready to Start Your Project?</h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              See why our clients trust us with their most important property projects. Contact us today for a free consultation and estimate.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="tel:9076009900"
                className="inline-flex items-center px-6 py-3 bg-primary hover:bg-primary/90 text-white font-semibold rounded-md transition-colors"
              >
                <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                </svg>
                Call Now
              </a>
              <a
                href="#contact"
                className="inline-flex items-center px-6 py-3 bg-accent hover:bg-accent/90 text-primary font-semibold rounded-md transition-colors"
              >
                Get Free Estimate
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default VideosPage;
