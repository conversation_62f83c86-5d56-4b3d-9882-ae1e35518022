import React from 'react';

const TeamPage = () => {
  const teamMembers = [
    {
      name: "<PERSON>",
      role: "Owner & Lead Contractor",
      image: "https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af742a253c12ce1cdb6.webp",
      bio: "With over 15 years of experience in property services, <PERSON> leads our team with expertise in all aspects of construction and remodeling.",
      specialties: ["Project Management", "Kitchen Remodeling", "Emergency Services"]
    },
    {
      name: "Licensed Plumber",
      role: "Plumbing Specialist",
      image: "https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af74a13d59a2324f42a.webp",
      bio: "Our licensed plumber ensures all plumbing work meets the highest standards and local codes.",
      specialties: ["Plumbing Repairs", "Fixture Installation", "Emergency Plumbing"]
    },
    {
      name: "Construction Crew",
      role: "Skilled Craftsmen",
      image: "https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af76b97b2a314fdc20a.webp",
      bio: "Our experienced crew brings years of expertise in construction, remodeling, and property maintenance.",
      specialties: ["Carpentry", "Drywall", "Painting", "Flooring"]
    }
  ];

  return (
    <div>
      {/* Hero Section */}
      <section className="relative h-[50vh] flex items-center">
        <div className="absolute inset-0">
          <img
            src="https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68506368d97df57e336772da.webp"
            alt="Essential Property Services Team"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-black opacity-60"></div>
        </div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold leading-tight mb-6 text-white">
              Our Team
            </h1>
            <p className="text-xl text-gray-200">
              Meet the skilled professionals behind our quality work
            </p>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-4">
              Experienced Professionals
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our team combines years of experience with a commitment to excellence, ensuring every project is completed to the highest standards.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {teamMembers.map((member, index) => (
              <div key={index} className="bg-white rounded-lg shadow-lg overflow-hidden">
                <div className="aspect-w-4 aspect-h-3">
                  <img
                    src={member.image}
                    alt={member.name}
                    className="w-full h-64 object-cover"
                  />
                </div>
                <div className="p-6">
                  <h3 className="text-xl font-bold text-primary mb-2">{member.name}</h3>
                  <p className="text-accent font-semibold mb-3">{member.role}</p>
                  <p className="text-gray-600 mb-4">{member.bio}</p>
                  
                  <div>
                    <h4 className="font-semibold text-primary mb-2">Specialties:</h4>
                    <div className="flex flex-wrap gap-2">
                      {member.specialties.map((specialty, idx) => (
                        <span
                          key={idx}
                          className="text-xs bg-accent/10 text-accent px-2 py-1 rounded"
                        >
                          {specialty}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Values Section */}
          <div className="mt-16">
            <h3 className="text-2xl font-bold text-center text-primary mb-8">Our Values</h3>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                  </svg>
                </div>
                <h4 className="text-xl font-bold text-primary mb-2">Quality</h4>
                <p className="text-gray-600">We never compromise on quality, ensuring every project meets our high standards.</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <circle cx="12" cy="12" r="10"></circle>
                    <polyline points="12 6 12 12 16 14"></polyline>
                  </svg>
                </div>
                <h4 className="text-xl font-bold text-primary mb-2">Reliability</h4>
                <p className="text-gray-600">We show up when we say we will and complete projects on time.</p>
              </div>
              
              <div className="text-center">
                <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                    <circle cx="12" cy="7" r="4"></circle>
                  </svg>
                </div>
                <h4 className="text-xl font-bold text-primary mb-2">Service</h4>
                <p className="text-gray-600">We serve people and property with wisdom and humility.</p>
              </div>
            </div>
          </div>

          <div className="mt-12 text-center">
            <a
              href="#contact"
              className="inline-flex items-center px-8 py-4 bg-accent hover:bg-accent/90 text-primary font-semibold rounded-md transition-colors"
            >
              Work With Our Team
            </a>
          </div>
        </div>
      </section>
    </div>
  );
};

export default TeamPage;
