import React, { useState } from 'react';

const PortfolioPage = () => {
  const [selectedProject, setSelectedProject] = useState(null);

  const projects = [
    {
      id: 1,
      title: "Kitchen Transformation",
      description: "Complete kitchen remodel with custom cabinets, modern appliances, and beautiful finishes",
      type: "Kitchen Remodel",
      beforeImage: "https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af7bd2be4faeaaf3744.webp",
      afterImage: "https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af74a13d584d124f429.webp",
      testimonial: {
        text: "<PERSON> and his crew are awesome. Professional in every aspect. They completed a kitchen remodel, walk-in shower, flooring, and smaller jobs for a pending sale on our home. Thank you <PERSON>, and thank you to your outstanding crew.",
        author: "<PERSON>",
        rating: 5,
      },
    },
    {
      id: 2,
      title: "Lux<PERSON> Shower Renovation",
      description: "Custom tiled walk-in shower with modern fixtures and professional installation",
      type: "Bathroom Remodel",
      beforeImage: "https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af7681f2d74071bd958.webp",
      afterImage: "https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af74a13d500e124f427.webp",
      testimonial: {
        text: "I have used Scott Cook on several projects now and APPRECIATE his responsiveness on our construction and remodel request(s). Scott also took care of a warranty item in a snap. I just referred a client this morning to Scott - he is that good.",
        author: "Brian Broderick",
        rating: 5,
      },
    },
    {
      id: 3,
      title: "Flood Damage Restoration",
      description: "Complete flood remediation and flooring restoration with precision and care",
      type: "Emergency Services",
      beforeImage: "https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af7d97df52dbc67c83e.webp",
      afterImage: "https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af790fc435f1db829c5.webp",
      testimonial: {
        text: "After a devastating flood damaged my home, I was in desperate need of a reliable contractor who could bring my house back to life. They worked quickly without sacrificing quality, which was especially important given the urgency after the flood. They got the job done in three days so my live in Nanny could arrive to a finished room.",
        author: "Jamie Tucker",
        rating: 5,
      },
    },
    {
      id: 4,
      title: "Window Sill Repair & Restoration",
      description: "Professional window sill repair and refinishing for improved functionality and appearance",
      type: "Home Maintenance",
      beforeImage: "https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af8681f2d348c1bd95e.webp",
      afterImage: "https://images.leadconnectorhq.com/image/f_webp/q_80/r_320/u_https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68507af74a13d584d124f429.webp",
      testimonial: {
        text: "Professional work completed on time and within budget. Highly recommend!",
        author: "Property Owner",
        rating: 5,
      },
    },
  ];

  return (
    <div>
      {/* Hero Section */}
      <section className="relative h-[50vh] flex items-center">
        <div className="absolute inset-0">
          <img
            src="https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/68506368d97df57e336772da.webp"
            alt="Essential Property Services Portfolio"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-black opacity-60"></div>
        </div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold leading-tight mb-6 text-white">
              Our Portfolio
            </h1>
            <p className="text-xl text-gray-200">
              See the quality and craftsmanship in our completed projects
            </p>
          </div>
        </div>
      </section>

      {/* Portfolio Grid */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-primary mb-4">
              Recent Projects
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Browse through our gallery of completed projects showcasing our commitment to quality and excellence.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-2 gap-8">
            {projects.map((project) => (
              <div key={project.id} className="bg-white rounded-lg shadow-lg overflow-hidden">
                <div className="relative">
                  <div className="grid grid-cols-2">
                    <div className="relative">
                      <img
                        src={project.beforeImage}
                        alt={`${project.title} - Before`}
                        className="w-full h-48 object-cover"
                      />
                      <div className="absolute top-2 left-2 bg-red-600 text-white px-2 py-1 rounded text-sm font-semibold">
                        Before
                      </div>
                    </div>
                    <div className="relative">
                      <img
                        src={project.afterImage}
                        alt={`${project.title} - After`}
                        className="w-full h-48 object-cover"
                      />
                      <div className="absolute top-2 left-2 bg-green-600 text-white px-2 py-1 rounded text-sm font-semibold">
                        After
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="p-6">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-accent bg-accent/10 px-2 py-1 rounded">
                      {project.type}
                    </span>
                    <div className="flex items-center">
                      {[...Array(project.testimonial.rating)].map((_, i) => (
                        <svg key={i} className="w-4 h-4 text-accent fill-current" viewBox="0 0 24 24">
                          <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
                        </svg>
                      ))}
                    </div>
                  </div>
                  
                  <h3 className="text-xl font-bold text-primary mb-2">{project.title}</h3>
                  <p className="text-gray-600 mb-4">{project.description}</p>
                  
                  <div className="border-t pt-4">
                    <p className="text-gray-700 italic text-sm mb-2">"{project.testimonial.text}"</p>
                    <p className="text-primary font-semibold text-sm">- {project.testimonial.author}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-12 text-center">
            <a
              href="#contact"
              className="inline-flex items-center px-8 py-4 bg-primary hover:bg-primary/90 text-white font-semibold rounded-md transition-colors"
            >
              Start Your Project Today
            </a>
          </div>
        </div>
      </section>
    </div>
  );
};

export default PortfolioPage;
