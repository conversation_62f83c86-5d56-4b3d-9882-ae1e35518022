<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Portfolio Upload - Essential Property Services</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Merriweather:ital,wght@0,400;0,700;1,400&display=swap"
      rel="stylesheet"
    />
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "#4B0082",
              accent: "#FFD700",
            },
            fontFamily: {
              sans: ['"Inter"', "system-ui", "-apple-system", "sans-serif"],
              serif: ['"Merriweather"', "Georgia", "serif"],
            },
          },
        },
      };
    </script>
    <style>
      html {
        scroll-behavior: smooth;
      }
      body {
        font-family: "Inter", system-ui, -apple-system, sans-serif;
      }

      input:focus,
      select:focus,
      textarea:focus {
        outline: none;
        box-shadow: 0 0 0 3px rgba(75, 0, 130, 0.2);
      }

      .file-upload-area {
        border: 2px dashed #d1d5db;
        transition: all 0.3s ease;
      }

      .file-upload-area:hover {
        border-color: #4B0082;
        background-color: #f8fafc;
      }

      .file-upload-area.dragover {
        border-color: #4B0082;
        background-color: #f3f4f6;
      }

      .file-preview {
        max-width: 150px;
        max-height: 150px;
        object-fit: cover;
        border-radius: 8px;
      }

      .remove-file {
        position: absolute;
        top: -8px;
        right: -8px;
        background: #ef4444;
        color: white;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 14px;
        font-weight: bold;
      }

      .remove-file:hover {
        background: #dc2626;
      }
    </style>
  </head>
  <body class="font-sans text-gray-800 bg-gray-50">
    <!-- Header -->
    <header
      id="header"
      class="fixed w-full z-50 transition-all duration-300 py-6"
    >
      <div class="container mx-auto px-4 md:px-6">
        <!-- Mobile: Small floating menu on right -->
        <div class="lg:hidden flex justify-end">
          <div
            id="navContainer"
            class="backdrop-blur-sm rounded-2xl px-4 py-3 transition-all duration-300 w-auto"
          >
            <!-- Mobile Menu Button -->
            <button
              id="mobileMenuBtn"
              class="text-white"
              onclick="toggleMobileMenu()"
            >
              <svg
                class="w-7 h-7"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <line x1="3" y1="12" x2="21" y2="12"></line>
                <line x1="3" y1="6" x2="21" y2="6"></line>
                <line x1="3" y1="18" x2="21" y2="18"></line>
              </svg>
            </button>
          </div>
        </div>

        <!-- Desktop: Full width navigation -->
        <div
          id="navContainerDesktop"
          class="hidden lg:block backdrop-blur-sm rounded-2xl px-6 py-4 transition-all duration-300"
        >
          <div class="flex justify-center items-center">
            <!-- Left Navigation -->
            <div class="flex items-center space-x-8 mr-auto">
              <nav>
                <ul class="flex space-x-8">
                  <li>
                    <a
                      href="index.html"
                      class="nav-link hover:text-accent transition text-white"
                      title="Home"
                    >
                      <svg
                        class="w-6 h-6"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                      >
                        <path
                          d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"
                        ></path>
                        <polyline points="9,22 9,12 15,12 15,22"></polyline>
                      </svg>
                    </a>
                  </li>
                  <li>
                    <a
                      href="services.html"
                      class="nav-link font-bold hover:text-accent transition text-white text-lg"
                      >Services</a
                    >
                  </li>
                  <li>
                    <a
                      href="portfolio.html"
                      class="nav-link font-bold hover:text-accent transition text-white text-lg"
                      >Portfolio</a
                    >
                  </li>
                  <li>
                    <a
                      href="team.html"
                      class="nav-link font-bold hover:text-accent transition text-white text-lg"
                      >Our Team</a
                    >
                  </li>
                </ul>
              </nav>
            </div>

            <!-- Right Navigation -->
            <div class="flex items-center space-x-4 ml-auto">
              <!-- Emergency Call Button -->
              <a
                href="tel:9076009900"
                class="flex items-center bg-primary hover:bg-primary/90 text-white px-4 py-3 rounded-md font-bold transition text-sm"
              >
                <svg
                  class="w-[18px] h-[18px] mr-2"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path
                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                  />
                </svg>
                Emergency 24/7
              </a>

              <!-- Maintenance Button -->
              <a
                href="index.html#contact"
                class="nav-link font-bold hover:bg-white/90 transition bg-white text-primary text-sm px-4 py-3 rounded-md"
                >Maintenance</a
              >

              <!-- Estimate Button -->
              <a
                href="index.html#scheduling"
                class="flex items-center bg-accent hover:bg-accent/90 text-primary px-4 py-3 rounded-md font-bold transition text-sm"
              >
                <svg
                  class="w-[18px] h-[18px] mr-2"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                  <line x1="16" y1="2" x2="16" y2="6"></line>
                  <line x1="8" y1="2" x2="8" y2="6"></line>
                  <line x1="3" y1="10" x2="21" y2="10"></line>
                </svg>
                Estimate
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Mobile menu -->
      <div
        id="mobileMenu"
        class="hidden lg:hidden bg-white/95 backdrop-blur-sm shadow-lg fixed top-0 left-0 right-0 z-50 mt-20 max-h-[calc(100vh-5rem)] overflow-y-auto rounded-b-2xl mx-4"
      >
        <nav class="px-6 py-6">
          <ul class="space-y-4">
            <li>
              <a
                href="index.html"
                class="flex items-center py-3 text-gray-800 font-medium hover:text-primary text-lg"
                onclick="closeMobileMenu()"
              >
                <svg
                  class="w-5 h-5 mr-3"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path
                    d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"
                  ></path>
                  <polyline points="9,22 9,12 15,12 15,22"></polyline>
                </svg>
                Home
              </a>
            </li>
            <li>
              <a
                href="services.html"
                class="block py-3 text-gray-800 font-medium hover:text-primary text-lg"
                onclick="closeMobileMenu()"
              >
                Services
              </a>
            </li>
            <li>
              <a
                href="portfolio.html"
                class="block py-3 text-gray-800 font-medium hover:text-primary text-lg"
                onclick="closeMobileMenu()"
              >
                Portfolio
              </a>
            </li>
            <li>
              <a
                href="team.html"
                class="block py-3 text-gray-800 font-medium hover:text-primary text-lg"
                onclick="closeMobileMenu()"
              >
                Our Team
              </a>
            </li>
          </ul>
        </nav>
      </div>
    </header>

    <div class="min-h-screen pt-32 pb-16">
      <!-- Hero Section -->
      <section class="relative py-16 bg-gradient-to-br from-primary to-primary/80">
        <div class="container mx-auto px-4">
          <div class="max-w-4xl mx-auto text-center text-white">
            <h1 class="text-4xl md:text-5xl font-bold mb-6">
              Portfolio Upload
            </h1>
            <p class="text-xl mb-8 opacity-90">
              Submit your project photos and details for the portfolio
            </p>
          </div>
        </div>
      </section>

      <!-- Upload Form Section -->
      <section class="py-16">
        <div class="container mx-auto px-4">
          <div class="max-w-4xl mx-auto">
            <div class="bg-white rounded-lg shadow-lg p-8">
              <form id="portfolioUploadForm" class="space-y-8">
                <!-- Project Information -->
                <div class="space-y-6">
                  <h2 class="text-2xl font-bold text-primary mb-6">Project Information</h2>

                  <div>
                    <label for="projectTitle" class="block text-sm font-medium text-gray-700 mb-2">
                      Project Title *
                    </label>
                    <input
                      type="text"
                      id="projectTitle"
                      name="projectTitle"
                      required
                      class="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent transition-colors"
                      placeholder="e.g., Modern Kitchen Transformation"
                    />
                  </div>

                  <div>
                    <label for="projectDescription" class="block text-sm font-medium text-gray-700 mb-2">
                      Project Description *
                    </label>
                    <textarea
                      id="projectDescription"
                      name="projectDescription"
                      required
                      rows="4"
                      class="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent transition-colors"
                      placeholder="Describe the project details, materials used, scope of work, etc."
                    ></textarea>
                  </div>

                  <div>
                    <label for="projectType" class="block text-sm font-medium text-gray-700 mb-2">
                      Project Type *
                    </label>
                    <select
                      id="projectType"
                      name="projectType"
                      required
                      class="w-full px-4 py-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent transition-colors"
                    >
                      <option value="">Select project type</option>
                      <option value="Kitchen Remodel">Kitchen Remodel</option>
                      <option value="Bathroom Remodel">Bathroom Remodel</option>
                      <option value="Outdoor Living">Outdoor Living</option>
                      <option value="Flooring Installation">Flooring Installation</option>
                      <option value="Home Addition">Home Addition</option>
                      <option value="Exterior Renovation">Exterior Renovation</option>
                      <option value="Interior Renovation">Interior Renovation</option>
                      <option value="Space Conversion">Space Conversion</option>
                      <option value="Emergency Services">Emergency Services</option>
                      <option value="Home Maintenance">Home Maintenance</option>
                      <option value="Full Remodel">Full Remodel</option>
                      <option value="Painting">Painting</option>
                      <option value="Structural Work">Structural Work</option>
                      <option value="Excavation">Excavation</option>
                      <option value="Property Management">Property Management</option>
                      <option value="Other">Other</option>
                    </select>
                  </div>
                </div>

                <!-- Before Images -->
                <div class="space-y-6">
                  <h2 class="text-2xl font-bold text-primary mb-6">Before Images *</h2>
                  <p class="text-gray-600 mb-4">Upload images showing the project area before work began</p>

                  <div class="file-upload-area border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                    <input
                      type="file"
                      id="beforeImages"
                      name="beforeImages"
                      multiple
                      accept="image/*"
                      class="hidden"
                      onchange="handleFileUpload(this, 'beforePreview')"
                    />
                    <label for="beforeImages" class="cursor-pointer">
                      <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                      </svg>
                      <span class="text-lg font-medium text-gray-700">Click to upload before images</span>
                      <p class="text-gray-500 mt-2">or drag and drop files here</p>
                      <p class="text-sm text-gray-400 mt-1">PNG, JPG, JPEG up to 10MB each</p>
                    </label>
                  </div>

                  <div id="beforePreview" class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4"></div>
                </div>

                <!-- After Images -->
                <div class="space-y-6">
                  <h2 class="text-2xl font-bold text-primary mb-6">After Images *</h2>
                  <p class="text-gray-600 mb-4">Upload images showing the completed project</p>

                  <div class="file-upload-area border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                    <input
                      type="file"
                      id="afterImages"
                      name="afterImages"
                      multiple
                      accept="image/*"
                      class="hidden"
                      onchange="handleFileUpload(this, 'afterPreview')"
                    />
                    <label for="afterImages" class="cursor-pointer">
                      <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                      </svg>
                      <span class="text-lg font-medium text-gray-700">Click to upload after images</span>
                      <p class="text-gray-500 mt-2">or drag and drop files here</p>
                      <p class="text-sm text-gray-400 mt-1">PNG, JPG, JPEG up to 10MB each</p>
                    </label>
                  </div>

                  <div id="afterPreview" class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4"></div>
                </div>

                <!-- Videos (Optional) -->
                <div class="space-y-6">
                  <h2 class="text-2xl font-bold text-primary mb-6">Videos (Optional)</h2>
                  <p class="text-gray-600 mb-4">Upload any project videos, time-lapses, or progress videos</p>

                  <div class="file-upload-area border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                    <input
                      type="file"
                      id="projectVideos"
                      name="projectVideos"
                      multiple
                      accept="video/*"
                      class="hidden"
                      onchange="handleFileUpload(this, 'videoPreview')"
                    />
                    <label for="projectVideos" class="cursor-pointer">
                      <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                        <path d="M15.75 10.5l4.72-4.72a.75.75 0 011.28.53v26.38a.75.75 0 01-1.28.53l-4.72-4.72M4.5 18v12a3 3 0 003 3h9.75m-12.75-18h12.75m0 0L21 21m-6.75-3h12.75a3 3 0 013 3v12a3 3 0 01-3 3h-12.75m0-18v18" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                      </svg>
                      <span class="text-lg font-medium text-gray-700">Click to upload videos</span>
                      <p class="text-gray-500 mt-2">or drag and drop files here</p>
                      <p class="text-sm text-gray-400 mt-1">MP4, MOV, AVI up to 100MB each</p>
                    </label>
                  </div>

                  <div id="videoPreview" class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4"></div>
                </div>

                <!-- Submit Button -->
                <div class="pt-6 border-t border-gray-200">
                  <button
                    type="submit"
                    class="w-full bg-primary hover:bg-primary/90 text-white font-bold py-4 px-8 rounded-md transition-colors duration-200 text-lg"
                  >
                    Submit Portfolio Project
                  </button>
                  <p class="text-sm text-gray-500 mt-3 text-center">
                    All fields marked with * are required. Files will be reviewed before being added to the portfolio.
                  </p>
                </div>
              </form>
            </div>
          </div>
        </div>
      </section>
    </div>

    <!-- Footer -->
    <footer class="bg-primary text-white py-12">
      <div class="container mx-auto px-4 md:px-6">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
          <div>
            <img
              src="https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/681586cfdb0184d1a887b9de.svg"
              alt="Essential Property Services Logo"
              class="h-16 mb-4"
            />
            <p class="text-gray-300 mb-4">
              Created to serve with excellence, through humility and
              professionalism.
            </p>
          </div>

          <div>
            <h3 class="text-lg font-semibold mb-4">Contact Information</h3>
            <ul class="space-y-3">
              <li class="flex items-center">
                <svg
                  class="w-[18px] h-[18px] mr-3 text-accent"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                  ></path>
                </svg>
                <a href="tel:9076009900" class="hover:text-accent transition"
                  >(*************</a
                >
              </li>
              <li class="flex items-center">
                <svg
                  class="w-[18px] h-[18px] mr-3 text-accent"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
                  ></path>
                  <polyline points="22,6 12,13 2,6"></polyline>
                </svg>
                <a
                  href="mailto:<EMAIL>"
                  class="hover:text-accent transition"
                  ><EMAIL></a
                >
              </li>
              <li class="flex items-start">
                <svg
                  class="w-[18px] h-[18px] mr-3 text-accent mt-1"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                  <circle cx="12" cy="10" r="3"></circle>
                </svg>
                <span>Serving Anchorage, AK & Surrounding Areas</span>
              </li>
            </ul>
          </div>

          <div>
            <h3 class="text-lg font-semibold mb-4">Licensing & Information</h3>
            <ul class="space-y-2 text-gray-300">
              <li>Alaska General Contractor License 217482</li>
              <li>MOA License CON14200</li>
              <li>Fully Insured and Bonded</li>
              <li>Available 24/7/364</li>
            </ul>
          </div>
        </div>

        <div
          class="border-t border-gray-700 pt-8 mt-8 text-gray-300 text-sm text-center"
        >
          <p>
            ©
            <script>
              document.write(new Date().getFullYear());
            </script>
            Essential Property Services LLC. All rights reserved.
          </p>
        </div>
      </div>
    </footer>

    <script>
      // Header scroll effect
      window.addEventListener("scroll", () => {
        const header = document.getElementById("header");
        const navContainer = document.getElementById("navContainer");
        const navContainerDesktop = document.getElementById("navContainerDesktop");
        const navLinks = document.querySelectorAll(".nav-link");
        const mobileMenuBtn = document.getElementById("mobileMenuBtn");

        if (window.scrollY > 20) {
          header.classList.remove("py-6");
          header.classList.add("py-4");

          if (navContainer) {
            navContainer.classList.add("bg-white/95", "shadow-lg");
          }
          if (navContainerDesktop) {
            navContainerDesktop.classList.add("bg-white/95", "shadow-lg");
          }

          navLinks.forEach((link) => {
            if (link.textContent.trim() === "Maintenance") {
              link.classList.remove("text-white", "hover:text-accent", "bg-white", "hover:bg-white/90");
              link.classList.add("text-primary", "bg-gray-100", "hover:bg-gray-200");
            } else {
              link.classList.remove("text-white", "hover:text-accent");
              link.classList.add("text-gray-800", "hover:text-primary");
            }
          });

          if (mobileMenuBtn) {
            mobileMenuBtn.classList.remove("text-white");
            mobileMenuBtn.classList.add("text-primary");
          }
        } else {
          header.classList.add("py-6");
          header.classList.remove("py-4");

          if (navContainer) {
            navContainer.classList.remove("bg-white/95", "shadow-lg");
          }
          if (navContainerDesktop) {
            navContainerDesktop.classList.remove("bg-white/95", "shadow-lg");
          }

          navLinks.forEach((link) => {
            if (link.textContent.trim() === "Maintenance") {
              link.classList.remove("text-primary", "bg-gray-100", "hover:bg-gray-200");
              link.classList.add("text-primary", "bg-white", "hover:bg-white/90");
            } else {
              link.classList.remove("text-gray-800", "hover:text-primary");
              link.classList.add("text-white", "hover:text-accent");
            }
          });

          if (mobileMenuBtn) {
            mobileMenuBtn.classList.remove("text-primary");
            mobileMenuBtn.classList.add("text-white");
          }
        }
      });

      // Mobile menu toggle
      function toggleMobileMenu() {
        const mobileMenu = document.getElementById("mobileMenu");
        const header = document.getElementById("header");
        const headerHeight = header.offsetHeight;
        mobileMenu.style.marginTop = headerHeight + "px";
        mobileMenu.classList.toggle("hidden");
      }

      function closeMobileMenu() {
        const mobileMenu = document.getElementById("mobileMenu");
        mobileMenu.classList.add("hidden");
      }

      // File upload handling
      function handleFileUpload(input, previewId) {
        const files = Array.from(input.files);
        const previewContainer = document.getElementById(previewId);

        files.forEach(file => {
          if (file.type.startsWith('image/')) {
            createImagePreview(file, previewContainer);
          } else if (file.type.startsWith('video/')) {
            createVideoPreview(file, previewContainer);
          }
        });
      }

      function createImagePreview(file, container) {
        const reader = new FileReader();
        reader.onload = function(e) {
          const previewDiv = document.createElement('div');
          previewDiv.className = 'relative';
          previewDiv.innerHTML = `
            <img src="${e.target.result}" alt="Preview" class="file-preview border border-gray-200">
            <button type="button" class="remove-file" onclick="removePreview(this)">×</button>
            <p class="text-xs text-gray-500 mt-1 truncate">${file.name}</p>
          `;
          container.appendChild(previewDiv);
        };
        reader.readAsDataURL(file);
      }

      function createVideoPreview(file, container) {
        const previewDiv = document.createElement('div');
        previewDiv.className = 'relative';
        previewDiv.innerHTML = `
          <div class="file-preview border border-gray-200 bg-gray-100 flex items-center justify-center">
            <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
            </svg>
          </div>
          <button type="button" class="remove-file" onclick="removePreview(this)">×</button>
          <p class="text-xs text-gray-500 mt-1 truncate">${file.name}</p>
        `;
        container.appendChild(previewDiv);
      }

      function removePreview(button) {
        button.parentElement.remove();
      }

      // Drag and drop functionality
      document.addEventListener('DOMContentLoaded', function() {
        const uploadAreas = document.querySelectorAll('.file-upload-area');

        uploadAreas.forEach(area => {
          area.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('dragover');
          });

          area.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');
          });

          area.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('dragover');

            const input = this.querySelector('input[type="file"]');
            const files = e.dataTransfer.files;

            // Create a new FileList-like object
            const dt = new DataTransfer();
            for (let i = 0; i < files.length; i++) {
              dt.items.add(files[i]);
            }
            input.files = dt.files;

            // Trigger the change event
            const previewId = input.getAttribute('onchange').match(/'([^']+)'/)[1];
            handleFileUpload(input, previewId);
          });
        });

        // Form submission
        document.getElementById('portfolioUploadForm').addEventListener('submit', async function(e) {
          e.preventDefault();

          const submitButton = this.querySelector('button[type="submit"]');
          const originalText = submitButton.textContent;

          // Validate required fields
          const projectTitle = document.getElementById('projectTitle').value;
          const projectDescription = document.getElementById('projectDescription').value;
          const projectType = document.getElementById('projectType').value;
          const beforeImages = document.getElementById('beforeImages').files;
          const afterImages = document.getElementById('afterImages').files;

          if (!projectTitle || !projectDescription || !projectType || beforeImages.length === 0 || afterImages.length === 0) {
            alert('Please fill in all required fields and upload at least one before and after image.');
            return;
          }

          // Show loading state
          submitButton.disabled = true;
          submitButton.textContent = 'Uploading...';

          try {
            const formData = new FormData();

            // Add text fields
            formData.append('projectTitle', projectTitle);
            formData.append('projectDescription', projectDescription);
            formData.append('projectType', projectType);

            // Add files
            const videos = document.getElementById('projectVideos').files;

            for (let i = 0; i < beforeImages.length; i++) {
              formData.append('beforeImages', beforeImages[i]);
            }

            for (let i = 0; i < afterImages.length; i++) {
              formData.append('afterImages', afterImages[i]);
            }

            for (let i = 0; i < videos.length; i++) {
              formData.append('videos', videos[i]);
            }

            // Submit to Netlify function
            const response = await fetch('/.netlify/functions/upload-cloudinary', {
              method: 'POST',
              body: formData
            });

            const result = await response.json();

            if (result.success) {
              alert('Portfolio submission successful! Your project will be reviewed and added to the portfolio soon.');

              // Reset form
              this.reset();
              document.getElementById('beforePreview').innerHTML = '';
              document.getElementById('afterPreview').innerHTML = '';
              document.getElementById('videoPreview').innerHTML = '';
            } else {
              throw new Error(result.error || 'Upload failed');
            }

          } catch (error) {
            console.error('Upload error:', error);
            alert('Upload failed: ' + error.message + '. Please try again.');
          } finally {
            // Restore button state
            submitButton.disabled = false;
            submitButton.textContent = originalText;
          }
        });
      });
    </script>
  </body>
</html>
