{"name": "essential", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.12", "@types/react": "^19.1.12", "@types/react-dom": "^19.1.9", "@vitejs/plugin-react": "^5.0.2", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.12", "typescript": "^5.9.2", "vite": "^4.5.2"}, "dependencies": {"lucide-react": "^0.542.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-router-dom": "^7.8.2"}}